#!/usr/bin/env python3
"""Convert USFM Bible files to JSONL format.

This script converts USFM (Unified Standard Format Markers) Bible files
to JSONL format with one verse per line. It supports both single translation
and batch processing modes.

Usage:
    poetry run python scripts/convert_to_json.py \
        --source raw/eng-web \
        --lang en --version web \
        --out data/verses_en_web.jsonl

    # Batch mode
    poetry run python scripts/convert_to_json.py --batch raw/
"""

import argparse
import csv
import json
import os
import re
import sys
import zipfile
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Optional, Tuple


def load_canonical_verse_counts() -> Dict[str, Dict[int, int]]:
    """Load canonical verse counts from CSV file.
    
    Returns:
        Dictionary mapping book -> chapter -> expected verse count
    """
    canon_file = Path(__file__).parent / "canon_verses.csv"
    verse_counts = defaultdict(dict)
    
    with open(canon_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            book = row['book']
            chapter = int(row['chapter'])
            verses = int(row['verses'])
            verse_counts[book][chapter] = verses
    
    return verse_counts


def get_usfm_content(source_path: Path) -> List[Tuple[str, str]]:
    """Get USFM file content from source directory.

    Args:
        source_path: Path to source directory containing USFM files or ZIP

    Returns:
        List of tuples (filename, content)
    """
    usfm_content = []

    # Check if source is a directory with extracted files
    if source_path.is_dir():
        for usfm_file in source_path.glob("*.usfm"):
            try:
                with open(usfm_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                usfm_content.append((usfm_file.name, content))
            except UnicodeDecodeError:
                # Try with different encoding
                with open(usfm_file, 'r', encoding='latin-1') as f:
                    content = f.read()
                usfm_content.append((usfm_file.name, content))

        # Also check for ZIP files to read directly
        for zip_file in source_path.glob("*.zip"):
            if "usfm" in zip_file.name.lower():
                with zipfile.ZipFile(zip_file, 'r') as zf:
                    # Read .usfm files directly from ZIP, avoiding __MACOSX
                    for member in zf.namelist():
                        if member.endswith('.usfm') and not member.startswith('__MACOSX'):
                            filename = Path(member).name
                            try:
                                with zf.open(member) as f:
                                    content = f.read().decode('utf-8')
                                usfm_content.append((filename, content))
                            except UnicodeDecodeError:
                                try:
                                    with zf.open(member) as f:
                                        content = f.read().decode('latin-1')
                                    usfm_content.append((filename, content))
                                except Exception as e:
                                    print(f"Warning: Could not read {member}: {e}")
                                    continue

    return sorted(usfm_content)


def parse_usfm_content(filename: str, content: str) -> List[Dict[str, any]]:
    """Parse USFM content and extract verses.

    Args:
        filename: Name of the USFM file
        content: USFM file content

    Returns:
        List of verse dictionaries with book, chapter, verse, text
    """
    verses = []
    current_book = None
    current_chapter = None

    lines = content.split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Extract book ID
        if line.startswith('\\id '):
            # Extract book name from ID line
            id_parts = line.split()
            if len(id_parts) >= 2:
                book_code = id_parts[1]
                current_book = get_book_name(book_code)
        
        # Extract chapter
        elif line.startswith('\\c '):
            chapter_match = re.match(r'\\c\s+(\d+)', line)
            if chapter_match:
                current_chapter = int(chapter_match.group(1))
        
        # Extract verses
        elif '\\v ' in line and current_book and current_chapter is not None:
            # Handle verse markers
            verse_pattern = r'\\v\s+(\d+)\s+(.*)'
            verse_match = re.match(verse_pattern, line)
            
            if verse_match:
                verse_num = int(verse_match.group(1))
                verse_text = verse_match.group(2)
                
                # Clean up the verse text
                cleaned_text = clean_verse_text(verse_text)
                
                if cleaned_text.strip():  # Skip empty verses
                    verses.append({
                        'book': current_book,
                        'chapter': current_chapter,
                        'verse': verse_num,
                        'text': cleaned_text
                    })
    
    return verses


def get_book_name(book_code: str) -> str:
    """Convert USFM book code to full book name.
    
    Args:
        book_code: Three-letter book code (e.g., 'GEN', 'MAT')
        
    Returns:
        Full book name
    """
    book_mapping = {
        'GEN': 'Genesis', 'EXO': 'Exodus', 'LEV': 'Leviticus', 'NUM': 'Numbers', 'DEU': 'Deuteronomy',
        'JOS': 'Joshua', 'JDG': 'Judges', 'RUT': 'Ruth', '1SA': '1 Samuel', '2SA': '2 Samuel',
        '1KI': '1 Kings', '2KI': '2 Kings', '1CH': '1 Chronicles', '2CH': '2 Chronicles',
        'EZR': 'Ezra', 'NEH': 'Nehemiah', 'EST': 'Esther', 'JOB': 'Job', 'PSA': 'Psalms',
        'PRO': 'Proverbs', 'ECC': 'Ecclesiastes', 'SNG': 'Song of Solomon', 'ISA': 'Isaiah',
        'JER': 'Jeremiah', 'LAM': 'Lamentations', 'EZK': 'Ezekiel', 'DAN': 'Daniel',
        'HOS': 'Hosea', 'JOL': 'Joel', 'AMO': 'Amos', 'OBA': 'Obadiah', 'JON': 'Jonah',
        'MIC': 'Micah', 'NAM': 'Nahum', 'HAB': 'Habakkuk', 'ZEP': 'Zephaniah',
        'HAG': 'Haggai', 'ZEC': 'Zechariah', 'MAL': 'Malachi',
        'MAT': 'Matthew', 'MRK': 'Mark', 'LUK': 'Luke', 'JHN': 'John', 'ACT': 'Acts',
        'ROM': 'Romans', '1CO': '1 Corinthians', '2CO': '2 Corinthians', 'GAL': 'Galatians',
        'EPH': 'Ephesians', 'PHP': 'Philippians', 'COL': 'Colossians', '1TH': '1 Thessalonians',
        '2TH': '2 Thessalonians', '1TI': '1 Timothy', '2TI': '2 Timothy', 'TIT': 'Titus',
        'PHM': 'Philemon', 'HEB': 'Hebrews', 'JAS': 'James', '1PE': '1 Peter', '2PE': '2 Peter',
        '1JN': '1 John', '2JN': '2 John', '3JN': '3 John', 'JUD': 'Jude', 'REV': 'Revelation'
    }
    
    return book_mapping.get(book_code.upper(), book_code)


def clean_verse_text(text: str) -> str:
    """Clean verse text by removing USFM markers and formatting.

    Args:
        text: Raw verse text with USFM markers

    Returns:
        Cleaned text
    """
    # Remove strong's numbers and word markers
    text = re.sub(r'\\w\s+([^|]+)\|[^\\]*\\w\*', r'\1', text)

    # Remove footnotes (including complex nested ones)
    text = re.sub(r'\\f\s+[^\\]*\\f\*', '', text)

    # Remove cross-references
    text = re.sub(r'\\x\s+[^\\]*\\x\*', '', text)

    # Remove character formatting markers
    text = re.sub(r'\\[a-z]+\s*([^\\]*?)\\[a-z]+\*', r'\1', text)

    # Remove remaining USFM markers
    text = re.sub(r'\\[a-z]+\*?', '', text)

    # Remove + markers and Hebrew/Greek text markers
    text = re.sub(r'\+\s*\d+:\d+\s+[^"]*"[^"]*"[^"]*', '', text)
    text = re.sub(r'\\+wh[^\\]*\\+wh\*', '', text)

    # Clean up extra spaces and punctuation
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\s*,\s*,', ',', text)  # Remove double commas
    text = re.sub(r'\s*\.\s*\.', '.', text)  # Remove double periods
    text = text.strip()

    return text


def validate_verse_counts(verses: List[Dict], canonical_counts: Dict[str, Dict[int, int]], strict: bool = False) -> Tuple[bool, List[str]]:
    """Validate verse counts against canonical standards.

    Args:
        verses: List of parsed verses
        canonical_counts: Expected verse counts by book/chapter
        strict: If True, fail on any validation error. If False, only warn.

    Returns:
        Tuple of (is_valid, list_of_errors)
    """
    errors = []
    warnings = []

    # Count verses by book and chapter
    actual_counts = defaultdict(lambda: defaultdict(int))
    for verse in verses:
        actual_counts[verse['book']][verse['chapter']] += 1

    # Check against canonical counts
    for book, chapters in actual_counts.items():
        if book not in canonical_counts:
            # Skip apocryphal books that aren't in our canonical list
            apocryphal_books = {'TOB', 'JDT', 'ESG', 'WIS', 'SIR', 'BAR', '1MA', '2MA',
                              '1ES', 'MAN', 'PS2', '3MA', '2ES', '4MA', 'DAG'}
            if book in apocryphal_books:
                warnings.append(f"Skipping apocryphal book: {book}")
            else:
                errors.append(f"Unknown book: {book}")
            continue

        for chapter, count in chapters.items():
            expected = canonical_counts[book].get(chapter)
            if expected is None:
                errors.append(f"Unknown chapter: {book} {chapter}")
            elif count != expected:
                msg = f"Verse count mismatch in {book} {chapter}: expected {expected}, got {count}"
                if strict:
                    errors.append(msg)
                else:
                    warnings.append(msg)

    # Print warnings
    if warnings:
        print("Validation warnings:")
        for warning in warnings:
            print(f"  {warning}")

    return len(errors) == 0, errors


def write_jsonl(verses: List[Dict], output_path: Path) -> None:
    """Write verses to JSONL file.
    
    Args:
        verses: List of verse dictionaries
        output_path: Path to output JSONL file
    """
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        for verse in verses:
            json_line = json.dumps(verse, ensure_ascii=False)
            f.write(json_line + '\n')


def process_translation(source_path: Path, lang: str, version: str, output_path: Optional[Path] = None) -> int:
    """Process a single translation.
    
    Args:
        source_path: Path to source directory
        lang: Language code
        version: Version code
        output_path: Optional output path (auto-generated if None)
        
    Returns:
        Number of verses processed, or -1 on error
    """
    if output_path is None:
        output_path = Path(f"data/verses_{lang}_{version}.jsonl")
    
    print(f"Processing {source_path} -> {output_path}")
    
    # Load canonical verse counts
    canonical_counts = load_canonical_verse_counts()

    # Get USFM content
    usfm_content = get_usfm_content(source_path)
    if not usfm_content:
        print(f"Error: No USFM files found in {source_path}")
        return -1

    # Parse all USFM content
    all_verses = []
    for filename, content in usfm_content:
        verses = parse_usfm_content(filename, content)
        all_verses.extend(verses)

    if not all_verses:
        print(f"Error: No verses found in {source_path}")
        return -1

    # Filter out apocryphal books
    apocryphal_books = {'TOB', 'JDT', 'ESG', 'WIS', 'SIR', 'BAR', '1MA', '2MA',
                       '1ES', 'MAN', 'PS2', '3MA', '2ES', '4MA', 'DAG'}
    canonical_verses = [v for v in all_verses if v['book'] not in apocryphal_books]

    # Validate verse counts (non-strict mode for warnings only)
    is_valid, errors = validate_verse_counts(canonical_verses, canonical_counts, strict=False)
    if not is_valid:
        print("Validation errors:")
        for error in errors:
            print(f"  {error}")
        return -1
    
    # Write JSONL output
    write_jsonl(canonical_verses, output_path)

    verse_count = len(canonical_verses)
    print(f"Parsed {verse_count:,} verses → {output_path}")

    return verse_count


def detect_lang_version(folder_name: str) -> Tuple[str, str]:
    """Detect language and version from folder name.
    
    Args:
        folder_name: Name of the source folder
        
    Returns:
        Tuple of (language, version)
    """
    # Common patterns: eng-web, spa-rv1909, etc.
    if '-' in folder_name:
        parts = folder_name.split('-', 1)
        return parts[0], parts[1]
    
    # Fallback: use folder name as version, 'unknown' as language
    return 'unknown', folder_name


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Convert USFM Bible files to JSONL format",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    
    parser.add_argument(
        "--source", type=Path, help="Path to source directory containing USFM files"
    )
    parser.add_argument(
        "--lang", type=str, help="Language code (e.g., 'en', 'es')"
    )
    parser.add_argument(
        "--version", type=str, help="Version code (e.g., 'web', 'kjv')"
    )
    parser.add_argument(
        "--out", type=Path, help="Output JSONL file path"
    )
    parser.add_argument(
        "--batch", type=Path, help="Batch mode: process all subdirectories in given path"
    )
    
    args = parser.parse_args()
    
    if args.batch:
        # Batch processing mode
        batch_path = args.batch
        if not batch_path.is_dir():
            print(f"Error: Batch path {batch_path} is not a directory")
            return 1
        
        total_verses = 0
        processed_count = 0
        
        for subdir in sorted(batch_path.iterdir()):
            if subdir.is_dir():
                lang, version = detect_lang_version(subdir.name)
                result = process_translation(subdir, lang, version)
                if result > 0:
                    total_verses += result
                    processed_count += 1
        
        print(f"\nBatch complete: {processed_count} translations, {total_verses:,} total verses")
        
    else:
        # Single translation mode
        if not all([args.source, args.lang, args.version]):
            print("Error: --source, --lang, and --version are required for single mode")
            return 1
        
        result = process_translation(args.source, args.lang, args.version, args.out)
        if result < 0:
            return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
